# 移动端页面映射分析

## 已存在的wapjsp目录结构
```
wapjsp/student/
├── courseSelectManagement/     # 选课管理
├── courseTableOfOtherSemester/ # 历史课程表
├── courseTableOfThisSemester/  # 本学期课表
├── examinationManagement/      # 考试管理
├── experiment/                 # 实验管理
├── finance/                    # 财务管理
├── integratedQuery/           # 综合查询
├── library/                   # 图书馆
├── main/                      # 主页面
├── personalManagement/        # 个人管理
├── query/                     # 查询功能
├── scholarship/               # 奖学金
├── service/                   # 服务功能
├── teachingEvaluation/        # 教学评价
└── thesis/                    # 论文管理
```

## urpSoft/WEB-INF/jsp/student目录结构
```
urpSoft/WEB-INF/jsp/student/
├── calendarSemesterCurriculum/    # 学期课程日历
├── certificationExam/             # 认证考试
├── courseSelectManagement/        # 选课管理 ✅
├── courseTableOfThisSemester/     # 本学期课表 ✅
├── courseVoluntary/               # 课程志愿
├── credibleReportCard/            # 可信成绩单
├── creditCheck/                   # 学分核查 ✅
├── creditTuition/                 # 学分学费 ✅
├── dropCourseCreditList/          # 退课学分列表
├── exam/                          # 考试
├── examinationManagement/         # 考试管理 ✅
├── exemptsExam/                   # 免试考试
├── experiment/                    # 实验 ✅
├── fileUpLoad/                    # 文件上传
├── graduateEntranceExamination/   # 研究生入学考试
├── graduatesManagement/           # 毕业生管理
├── innovationCredits/             # 创新学分 ✅
├── integratedQuery/               # 综合查询 ✅
├── internship/                    # 实习 ✅
├── laborEducation/                # 劳动教育
├── lnuinnovationCredits/          # 辽大创新学分
├── main/                          # 主页面 ✅
├── myAttention/                   # 我的关注
├── noticeManagement/              # 通知管理
├── personalManagement/            # 个人管理 ✅
├── postgraduate/                  # 研究生
├── practicing/                    # 实践
├── professionalCertification/     # 专业认证
├── schoolcalendar/                # 校历
├── studentCertificatePrinting/    # 学生证明打印 ✅
├── subjectCompetition/            # 学科竞赛
├── teachingEvaluation/            # 教学评价 ✅
├── teachingEvaluationGc/          # 教学评价(工程)
├── teachingResources/             # 教学资源 ✅
├── thesis/                        # 论文 ✅
├── trainProgram/                  # 培养方案 ✅
└── weekLySchedule/                # 周课表
```

## 缺失的移动端页面目录

需要创建以下目录和对应的移动端页面：

### 1. calendarSemesterCurriculum (学期课程日历)
- 功能：查看学期课程安排的日历视图
- 移动端路径：`wapjsp/student/calendarSemesterCurriculum/index.jsp`

### 2. certificationExam (认证考试)
- 功能：认证考试报名和查询
- 移动端路径：`wapjsp/student/certificationExam/index.jsp`

### 3. courseVoluntary (课程志愿)
- 功能：课程志愿填报
- 移动端路径：`wapjsp/student/courseVoluntary/index.jsp`

### 4. credibleReportCard (可信成绩单)
- 功能：可信成绩单申请和查看
- 移动端路径：`wapjsp/student/credibleReportCard/index.jsp`

### 5. dropCourseCreditList (退课学分列表)
- 功能：查看退课学分情况
- 移动端路径：`wapjsp/student/dropCourseCreditList/index.jsp`

### 6. exam (考试)
- 功能：考试相关功能
- 移动端路径：`wapjsp/student/exam/index.jsp`

### 7. exemptsExam (免试考试)
- 功能：免试考试申请
- 移动端路径：`wapjsp/student/exemptsExam/index.jsp`

### 8. fileUpLoad (文件上传)
- 功能：文件上传管理
- 移动端路径：`wapjsp/student/fileUpLoad/index.jsp`

### 9. graduateEntranceExamination (研究生入学考试)
- 功能：研究生入学考试相关
- 移动端路径：`wapjsp/student/graduateEntranceExamination/index.jsp`

### 10. graduatesManagement (毕业生管理)
- 功能：毕业生相关管理
- 移动端路径：`wapjsp/student/graduatesManagement/index.jsp`

### 11. laborEducation (劳动教育)
- 功能：劳动教育活动管理
- 移动端路径：`wapjsp/student/laborEducation/index.jsp`

### 12. lnuinnovationCredits (辽大创新学分)
- 功能：辽宁大学创新学分管理
- 移动端路径：`wapjsp/student/lnuinnovationCredits/index.jsp`

### 13. myAttention (我的关注)
- 功能：关注的内容管理
- 移动端路径：`wapjsp/student/myAttention/index.jsp`

### 14. noticeManagement (通知管理)
- 功能：通知公告管理
- 移动端路径：`wapjsp/student/noticeManagement/index.jsp`

### 15. postgraduate (研究生)
- 功能：研究生相关功能
- 移动端路径：`wapjsp/student/postgraduate/index.jsp`

### 16. practicing (实践)
- 功能：实践活动管理
- 移动端路径：`wapjsp/student/practicing/index.jsp`

### 17. professionalCertification (专业认证)
- 功能：专业认证相关
- 移动端路径：`wapjsp/student/professionalCertification/index.jsp`

### 18. schoolcalendar (校历)
- 功能：校历查看
- 移动端路径：`wapjsp/student/schoolcalendar/index.jsp`

### 19. subjectCompetition (学科竞赛)
- 功能：学科竞赛管理
- 移动端路径：`wapjsp/student/subjectCompetition/index.jsp`

### 20. teachingEvaluationGc (教学评价-工程)
- 功能：工程类教学评价
- 移动端路径：`wapjsp/student/teachingEvaluationGc/index.jsp`

### 21. weekLySchedule (周课表)
- 功能：周课表查看
- 移动端路径：`wapjsp/student/weekLySchedule/index.jsp`

## 总结

目前已完成54个移动端页面，还需要补充21个页面以实现与桌面端的完全一一对应。这些页面主要涵盖：

1. **学术管理类**：课程日历、认证考试、课程志愿等
2. **考试管理类**：免试考试、考试管理等
3. **学生服务类**：文件上传、通知管理、我的关注等
4. **专业功能类**：专业认证、学科竞赛、劳动教育等
5. **研究生功能**：研究生管理、入学考试等

完成这21个页面后，移动端将达到75个页面，实现与桌面端的完全对应。
