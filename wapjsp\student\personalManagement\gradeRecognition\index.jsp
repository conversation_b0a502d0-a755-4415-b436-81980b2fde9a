<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>成绩认定申请</title>
    <link rel="stylesheet" href="/WEB-INF/css/phone/mobile-framework.css">
    <style>
        /* 成绩认定申请页面样式 */
        .recognition-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
            color: white;
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            padding: var(--padding-md);
        }
        
        .header-title {
            font-size: var(--font-size-h4);
            font-weight: 500;
            margin-bottom: var(--margin-sm);
            text-align: center;
        }
        
        .header-subtitle {
            font-size: var(--font-size-small);
            opacity: 0.9;
            text-align: center;
        }
        
        .recognition-types {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .type-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
            display: flex;
            align-items: center;
        }
        
        .type-item:last-child {
            border-bottom: none;
        }
        
        .type-item:active {
            background: var(--bg-color-active);
        }
        
        .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            color: white;
        }
        
        .type-icon.transfer {
            background: var(--success-color);
        }
        
        .type-icon.exchange {
            background: var(--info-color);
        }
        
        .type-icon.competition {
            background: var(--warning-color);
        }
        
        .type-icon.certificate {
            background: var(--error-color);
        }
        
        .type-icon.practice {
            background: var(--primary-color);
        }
        
        .type-content {
            flex: 1;
        }
        
        .type-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }
        
        .type-desc {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            line-height: var(--line-height-base);
        }
        
        .type-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            margin-left: var(--margin-sm);
        }
        
        .status-available {
            background: var(--success-color);
            color: white;
        }
        
        .status-limited {
            background: var(--warning-color);
            color: white;
        }
        
        .my-applications {
            background: var(--bg-primary);
            margin: var(--margin-sm) var(--margin-md);
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .applications-header {
            background: var(--bg-tertiary);
            padding: var(--padding-md);
            font-weight: 500;
            color: var(--text-primary);
            border-bottom: 1px solid var(--divider-color);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .applications-title {
            display: flex;
            align-items: center;
        }
        
        .applications-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .applications-count {
            background: var(--primary-color);
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: var(--font-size-mini);
        }
        
        .application-item {
            padding: var(--padding-md);
            border-bottom: 1px solid var(--divider-color);
            cursor: pointer;
            transition: background-color var(--transition-base);
        }
        
        .application-item:last-child {
            border-bottom: none;
        }
        
        .application-item:active {
            background: var(--bg-color-active);
        }
        
        .application-item.pending {
            border-left: 4px solid var(--warning-color);
        }
        
        .application-item.approved {
            border-left: 4px solid var(--success-color);
        }
        
        .application-item.rejected {
            border-left: 4px solid var(--error-color);
        }
        
        .application-basic {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: var(--margin-sm);
        }
        
        .application-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            flex: 1;
            margin-right: var(--margin-sm);
        }
        
        .application-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: var(--font-size-mini);
            font-weight: 500;
            white-space: nowrap;
        }
        
        .status-pending {
            background: var(--warning-color);
            color: white;
        }
        
        .status-approved {
            background: var(--success-color);
            color: white;
        }
        
        .status-rejected {
            background: var(--error-color);
            color: white;
        }
        
        .application-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-sm);
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-sm);
        }
        
        .detail-item {
            display: flex;
            justify-content: space-between;
        }
        
        .course-info {
            background: var(--bg-tertiary);
            border-radius: 6px;
            padding: var(--padding-sm);
            font-size: var(--font-size-small);
        }
        
        .course-title {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-xs);
        }
        
        .course-details {
            color: var(--text-secondary);
            display: flex;
            justify-content: space-between;
        }
        
        .recognition-form {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--bg-primary);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform var(--transition-base);
            overflow-y: auto;
        }
        
        .recognition-form.show {
            transform: translateX(0);
        }
        
        .form-header {
            background: var(--primary-color);
            color: white;
            padding: var(--padding-md);
            display: flex;
            align-items: center;
            position: sticky;
            top: 0;
            z-index: 10;
        }
        
        .form-back {
            margin-right: var(--margin-md);
            font-size: var(--font-size-h4);
            cursor: pointer;
        }
        
        .form-title {
            flex: 1;
            font-size: var(--font-size-base);
            font-weight: 500;
        }
        
        .form-content {
            padding: var(--padding-md);
        }
        
        .form-section {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: var(--padding-md);
            margin-bottom: var(--margin-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .section-title {
            font-size: var(--font-size-base);
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: var(--margin-md);
            display: flex;
            align-items: center;
        }
        
        .section-title i {
            margin-right: var(--margin-xs);
            color: var(--primary-color);
        }
        
        .form-group {
            margin-bottom: var(--margin-md);
        }
        
        .form-group:last-child {
            margin-bottom: 0;
        }
        
        .form-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            margin-bottom: var(--margin-xs);
            font-weight: 500;
        }
        
        .form-label.required::after {
            content: '*';
            color: var(--error-color);
            margin-left: 4px;
        }
        
        .form-input {
            width: 100%;
            min-height: 40px;
            padding: 8px 12px;
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            font-size: var(--font-size-base);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .form-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }
        
        .form-textarea {
            min-height: 80px;
            resize: vertical;
        }
        
        .course-mapping {
            border: 1px solid var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-md);
            background: var(--bg-tertiary);
        }
        
        .mapping-row {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--margin-sm);
        }
        
        .mapping-row:last-child {
            margin-bottom: 0;
        }
        
        .mapping-label {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
            min-width: 80px;
        }
        
        .mapping-arrow {
            color: var(--primary-color);
            font-size: var(--font-size-base);
        }
        
        .form-upload {
            border: 2px dashed var(--border-primary);
            border-radius: 6px;
            padding: var(--padding-lg);
            text-align: center;
            cursor: pointer;
            transition: all var(--transition-base);
        }
        
        .form-upload:hover {
            border-color: var(--primary-color);
            background: var(--bg-tertiary);
        }
        
        .upload-icon {
            font-size: var(--font-size-h2);
            color: var(--text-disabled);
            margin-bottom: var(--margin-sm);
        }
        
        .upload-text {
            font-size: var(--font-size-small);
            color: var(--text-secondary);
        }
        
        .file-list {
            margin-top: var(--margin-sm);
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: var(--padding-sm);
            background: var(--bg-tertiary);
            border-radius: 6px;
            margin-bottom: var(--margin-xs);
        }
        
        .file-item:last-child {
            margin-bottom: 0;
        }
        
        .file-icon {
            margin-right: var(--margin-sm);
            color: var(--primary-color);
        }
        
        .file-name {
            flex: 1;
            font-size: var(--font-size-small);
            color: var(--text-primary);
        }
        
        .file-remove {
            color: var(--error-color);
            cursor: pointer;
        }
        
        .form-actions {
            background: var(--bg-primary);
            padding: var(--padding-md);
            border-top: 1px solid var(--divider-color);
            position: sticky;
            bottom: 0;
            display: flex;
            gap: var(--spacing-md);
        }
        
        .btn-submit {
            background: var(--success-color);
            color: white;
        }
        
        .btn-draft {
            background: var(--warning-color);
            color: white;
        }
        
        .btn-cancel {
            background: var(--text-disabled);
            color: white;
        }
    </style>
</head>
<body>
    <div class="page-mobile">
        <!-- 导航栏 -->
        <nav class="navbar-mobile">
            <div class="navbar-back" onclick="parent.closeFrame();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="navbar-title">成绩认定申请</div>
            <div class="navbar-action" onclick="refreshData();">
                <i class="ace-icon fa fa-refresh"></i>
            </div>
        </nav>

        <!-- 页面头部 -->
        <div class="recognition-header">
            <div class="header-title">成绩认定申请</div>
            <div class="header-subtitle">申请转学分、交换学习、竞赛等成绩认定</div>
        </div>

        <!-- 认定类型 -->
        <div class="recognition-types">
            <div class="type-item" onclick="showRecognitionForm('transfer')">
                <div class="type-icon transfer">
                    <i class="ace-icon fa fa-exchange"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">转学分认定</div>
                    <div class="type-desc">其他学校修读课程学分认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('exchange')">
                <div class="type-icon exchange">
                    <i class="ace-icon fa fa-globe"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">交换学习认定</div>
                    <div class="type-desc">国内外交换学习成绩认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('competition')">
                <div class="type-icon competition">
                    <i class="ace-icon fa fa-trophy"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">竞赛成绩认定</div>
                    <div class="type-desc">学科竞赛、创新创业等成绩认定</div>
                </div>
                <div class="type-status status-limited">有限制</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('certificate')">
                <div class="type-icon certificate">
                    <i class="ace-icon fa fa-certificate"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">证书认定</div>
                    <div class="type-desc">职业资格证书、技能证书认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>

            <div class="type-item" onclick="showRecognitionForm('practice')">
                <div class="type-icon practice">
                    <i class="ace-icon fa fa-briefcase"></i>
                </div>
                <div class="type-content">
                    <div class="type-title">实践成绩认定</div>
                    <div class="type-desc">社会实践、志愿服务等认定</div>
                </div>
                <div class="type-status status-available">可申请</div>
            </div>
        </div>

        <!-- 我的申请 -->
        <div class="my-applications">
            <div class="applications-header">
                <div class="applications-title">
                    <i class="ace-icon fa fa-file-text"></i>
                    <span>我的申请</span>
                </div>
                <div class="applications-count" id="applicationsCount">0</div>
            </div>

            <div id="applicationsList">
                <!-- 申请列表将通过JavaScript动态填充 -->
            </div>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="emptyState" style="display: none;">
            <i class="ace-icon fa fa-file-o"></i>
            <div id="emptyMessage">暂无认定申请记录</div>
        </div>

        <!-- 加载状态 -->
        <div class="loading-container" id="loadingState" style="display: none;">
            <i class="ace-icon fa fa-spinner fa-spin"></i>
            <span>加载中...</span>
        </div>
    </div>

    <!-- 认定申请表单 -->
    <div class="recognition-form" id="recognitionForm">
        <div class="form-header">
            <div class="form-back" onclick="closeRecognitionForm();">
                <i class="ace-icon fa fa-arrow-left"></i>
            </div>
            <div class="form-title" id="formTitle">成绩认定申请</div>
        </div>

        <div class="form-content">
            <!-- 基本信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-user"></i>
                    <span>基本信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label">认定类型</div>
                    <input type="text" class="form-input" id="recognitionType" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">学号</div>
                    <input type="text" class="form-input" id="studentId" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">姓名</div>
                    <input type="text" class="form-input" id="studentName" readonly>
                </div>

                <div class="form-group">
                    <div class="form-label">专业班级</div>
                    <input type="text" class="form-input" id="majorClass" readonly>
                </div>
            </div>

            <!-- 原始成绩信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-graduation-cap"></i>
                    <span>原始成绩信息</span>
                </div>

                <div class="form-group" id="sourceSchoolGroup">
                    <div class="form-label required">来源学校/机构</div>
                    <input type="text" class="form-input" id="sourceSchool" placeholder="请输入学校或机构名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">课程/项目名称</div>
                    <input type="text" class="form-input" id="sourceCourse" placeholder="请输入课程或项目名称">
                </div>

                <div class="form-group">
                    <div class="form-label required">获得成绩/等级</div>
                    <input type="text" class="form-input" id="sourceGrade" placeholder="请输入成绩或等级">
                </div>

                <div class="form-group">
                    <div class="form-label required">学分/学时</div>
                    <input type="number" class="form-input" id="sourceCredit" placeholder="请输入学分或学时数" step="0.5">
                </div>

                <div class="form-group">
                    <div class="form-label required">完成时间</div>
                    <input type="date" class="form-input" id="completionDate">
                </div>
            </div>

            <!-- 目标课程信息 -->
            <div class="form-section" id="targetCourseSection">
                <div class="section-title">
                    <i class="ace-icon fa fa-bullseye"></i>
                    <span>目标课程信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">目标课程</div>
                    <select class="form-input" id="targetCourse">
                        <option value="">请选择要认定的课程</option>
                    </select>
                </div>

                <div class="form-group">
                    <div class="form-label">课程映射关系</div>
                    <div class="course-mapping" id="courseMapping">
                        <div class="mapping-row">
                            <span class="mapping-label">原课程:</span>
                            <span id="sourceCourseDisplay">-</span>
                            <span class="mapping-arrow">→</span>
                            <span id="targetCourseDisplay">-</span>
                        </div>
                        <div class="mapping-row">
                            <span class="mapping-label">学分:</span>
                            <span id="sourceCreditDisplay">-</span>
                            <span class="mapping-arrow">→</span>
                            <span id="targetCreditDisplay">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申请说明 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-edit"></i>
                    <span>申请说明</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">认定理由</div>
                    <textarea class="form-input form-textarea" id="recognitionReason"
                              placeholder="请详细说明申请认定的理由和依据..."></textarea>
                </div>

                <div class="form-group">
                    <div class="form-label">课程内容对比</div>
                    <textarea class="form-input form-textarea" id="contentComparison"
                              placeholder="请对比说明原课程与目标课程的内容相似性..."></textarea>
                </div>
            </div>

            <!-- 联系信息 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-phone"></i>
                    <span>联系信息</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">联系电话</div>
                    <input type="tel" class="form-input" id="contactPhone" placeholder="请输入联系电话">
                </div>

                <div class="form-group">
                    <div class="form-label">邮箱地址</div>
                    <input type="email" class="form-input" id="email" placeholder="请输入邮箱地址">
                </div>
            </div>

            <!-- 附件上传 -->
            <div class="form-section">
                <div class="section-title">
                    <i class="ace-icon fa fa-paperclip"></i>
                    <span>证明材料</span>
                </div>

                <div class="form-group">
                    <div class="form-label required">上传附件</div>
                    <div class="form-upload" onclick="selectFiles()">
                        <div class="upload-icon">
                            <i class="ace-icon fa fa-cloud-upload"></i>
                        </div>
                        <div class="upload-text">点击上传成绩单、证书等证明材料</div>
                    </div>
                    <input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;">
                    <div class="file-list" id="fileList">
                        <!-- 文件列表将动态填充 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单操作 -->
        <div class="form-actions">
            <button class="btn-mobile btn-cancel flex-1" onclick="closeRecognitionForm();">取消</button>
            <button class="btn-mobile btn-draft flex-1" onclick="saveDraft();">保存草稿</button>
            <button class="btn-mobile btn-submit flex-1" onclick="submitRecognition();">提交申请</button>
        </div>
    </div>

    <script>
        // 全局变量
        let myApplications = [];
        let currentRecognitionType = '';
        let uploadedFiles = [];
        let studentInfo = {};
        let availableCourses = [];

        $(function() {
            initPage();
            loadStudentInfo();
            loadMyApplications();
        });

        // 初始化页面
        function initPage() {
            adjustPageHeight();
            bindFileUpload();
            bindFormEvents();
        }

        // 绑定文件上传
        function bindFileUpload() {
            $('#fileInput').change(function() {
                handleFileSelect(this.files);
            });
        }

        // 绑定表单事件
        function bindFormEvents() {
            $('#sourceCourse, #sourceCredit').on('input', updateCourseMapping);
            $('#targetCourse').change(updateCourseMapping);
        }
